import Grid from '@mui/material/Grid';
import Button from 'Components/Button'; // Assuming this is your custom button
import { useFormikContext } from 'formik';
import { steps } from './addTenantsUtils';

interface ButtonRenderProps {
  handleBack: () => void;
  handleNext: () => void;
  handleCancel: () => void;
  activeStep: number;
  nextButtonState?: boolean;
  isAddTenantLoader?: boolean;
  isFileUploaded?: boolean;
  isEdit?: boolean;
}

const RenderButton: React.FC<ButtonRenderProps> = props => {
  const { isValid, dirty, handleSubmit, values } = useFormikContext<any>();
  const {
    handleBack,
    handleNext,
    handleCancel,
    activeStep,
    nextButtonState = false,
    isFileUploaded = false,
    isAddTenantLoader = false,
    isEdit = false,
  } = props;

  // Function to check if all required fields for step 0 (Tenant Details) are filled
  const areRequiredFieldsFilled = () => {
    if (activeStep !== 0) return true; // Only validate for step 0

    const requiredFields = ['company', 'key', 'firstName', 'lastName', 'email', 'city', 'state'];
    const allFieldsFilled = requiredFields.every(field => {
      const value = values[field];
      return value && value.toString().trim() !== '';
    });

    // For the subdomain (key) field, we also need to ensure it's available
    // The validation schema should handle this, but we can add an extra check
    // if needed based on the subdomain availability state
    return allFieldsFilled;
  };


  const shouldDisableNext = isEdit
    ? nextButtonState
    : !dirty || !isValid || nextButtonState || !areRequiredFieldsFilled();

  const getBackButtonText = () => {
    if (activeStep === 1) return '< Tenant Details';
    if (activeStep === 3) return '< Plan Details';
    return 'Back';
  };

  const getActionButtonText = () => {
    if (activeStep === 0) return 'Plan Details >';
    if (activeStep === steps.length - 1) return isEdit ? 'Update Tenant' : 'Add Tenant';
    if (activeStep === 1) return 'Upload Documents >';
    return 'Next';
  };

  return (
    <Grid container spacing={2} sx={{ m: 1 }} alignItems="center">
      {/* ✅ Left Side: Back Button */}
      <Grid size={{ xs: 12, sm: 6 }}>
        {activeStep !== 0 && (
          <Button
            data-testid="back-button"
            variant="outlined"
            onClick={handleBack}
            disabled={isAddTenantLoader}
            sx={{ width: 200, height: 50, borderColor: '#e8e8e8ff', color: 'black' }}
          >
            {getBackButtonText()}
          </Button>
        )}
      </Grid>

      {/* ✅ Right Side: Cancel & Next/Submit */}
      <Grid
        size={{ xs: 12, sm: 6 }}
        sx={{
          display: 'flex',
          justifyContent: 'flex-end',
          gap: 1,
        }}
      >
        <Button
          data-testid="cancel-button"
          variant="outlined"
          name="cancel"
          onClick={handleCancel}
          disabled={isAddTenantLoader}
          sx={{ width: 100, height: 50, borderColor: '#e8e8e8ff', color: 'black' }}
        >
          Cancel
        </Button>

        {activeStep === steps.length - 1 ? (
          <Button
            data-testid="submit-button"
            variant="contained"
            onClick={() => handleSubmit()}
            isLoading={isAddTenantLoader} // Keep if using custom Button
            sx={{
              width: 150,
              height: 50,
              backgroundColor: '#173477',
              color: '#fff', // make sure text is readable
              '&:hover': {
                backgroundColor: '#142d66', // slightly darker shade for hover
              },
            }}
            disabled={!isFileUploaded}
          >
            {getActionButtonText()}
          </Button>
        ) : (
          <Button
            data-testid="next-button"
            variant="contained"
            color="primary"
            onClick={handleNext}
            sx={{
              width: 200,
              height: 50,
              backgroundColor: '#173477',
              color: '#fff', // make sure text is readable
              '&:hover': {
                backgroundColor: '#142d66', // slightly darker shade for hover
              },
            }}
            disabled={shouldDisableNext}
          >
            {getActionButtonText()}
          </Button>
        )}
      </Grid>
    </Grid>
  );
};

export default RenderButton;
